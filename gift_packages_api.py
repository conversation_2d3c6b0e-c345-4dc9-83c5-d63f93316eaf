
import logging
from datetime import datetime, date
import json
from flask import Blueprint, request, jsonify, session

# 创建蓝图
gift_packages_bp = Blueprint('gift_packages_api', __name__)

# 设置日志
logger = logging.getLogger("gift_packages_api")

# 这些函数将在app.py中被注入
def get_db_connection():
    """This function will be imported from app at runtime"""
    pass

def get_user_by_steamid(steamid, use_cache=True):
    """This function will be imported from app at runtime"""
    pass

def clear_user_cache(steamid):
    """This function will be imported from app at runtime"""
    pass

# --- 后台管理API ---

@gift_packages_bp.route('/api/admin/gift_packages', methods=['GET'])
def admin_get_gift_packages():
    """获取所有礼包列表（供后台管理使用）"""
    if 'steamid' not in session or not get_user_by_steamid(session['steamid']).get('is_admin'):
        return jsonify({'success': False, 'message': '无权访问'}), 403

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    try:
        with conn.cursor() as cursor:
            sql = "SELECT * FROM gift_packages ORDER BY type, vip_level_required"
            cursor.execute(sql)
            packages = cursor.fetchall()
            # 将items字段从JSON字符串转换为Python对象
            for package in packages:
                if isinstance(package.get('items'), str):
                    package['items'] = json.loads(package['items'])
            return jsonify({'success': True, 'packages': packages})
    except Exception as e:
        logger.error(f"后台获取礼包列表失败: {e}")
        return jsonify({'success': False, 'message': f'获取失败: {e}'}), 500
    finally:
        if conn:
            conn.close()

@gift_packages_bp.route('/api/admin/gift_packages', methods=['POST'])
def admin_create_gift_package():
    """创建新的礼包"""
    if 'steamid' not in session or not get_user_by_steamid(session['steamid']).get('is_admin'):
        return jsonify({'success': False, 'message': '无权访问'}), 403

    data = request.json
    name = data.get('name')
    description = data.get('description')
    type = data.get('type')
    vip_level_required = data.get('vip_level_required', 0)
    items = data.get('items')
    image_path = data.get('image_path')
    is_active = data.get('is_active', 1)

    if not all([name, type, items]):
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    try:
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO gift_packages (name, description, type, vip_level_required, items, image_path, is_active)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (name, description, type, vip_level_required, json.dumps(items), image_path, is_active))
        conn.commit()
        return jsonify({'success': True, 'message': '礼包创建成功'})
    except Exception as e:
        conn.rollback()
        logger.error(f"创建礼包失败: {e}")
        return jsonify({'success': False, 'message': f'创建失败: {e}'}), 500
    finally:
        if conn:
            conn.close()

@gift_packages_bp.route('/api/admin/gift_packages/<int:package_id>', methods=['PUT'])
def admin_update_gift_package(package_id):
    """更新指定的礼包"""
    if 'steamid' not in session or not get_user_by_steamid(session['steamid']).get('is_admin'):
        return jsonify({'success': False, 'message': '无权访问'}), 403

    data = request.json
    name = data.get('name')
    description = data.get('description')
    type = data.get('type')
    vip_level_required = data.get('vip_level_required', 0)
    items = data.get('items')
    image_path = data.get('image_path')
    is_active = data.get('is_active')

    if not all([name, type, items]):
        return jsonify({'success': False, 'message': '缺少必要参数'}), 400

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    try:
        with conn.cursor() as cursor:
            sql = """
            UPDATE gift_packages SET
            name = %s, description = %s, type = %s, vip_level_required = %s, items = %s, image_path = %s, is_active = %s
            WHERE id = %s
            """
            cursor.execute(sql, (name, description, type, vip_level_required, json.dumps(items), image_path, is_active, package_id))
        conn.commit()
        return jsonify({'success': True, 'message': '礼包更新成功'})
    except Exception as e:
        conn.rollback()
        logger.error(f"更新礼包失败: {e}")
        return jsonify({'success': False, 'message': f'更新失败: {e}'}), 500
    finally:
        if conn:
            conn.close()

@gift_packages_bp.route('/api/admin/gift_packages/<int:package_id>', methods=['DELETE'])
def admin_delete_gift_package(package_id):
    """删除指定的礼包"""
    if 'steamid' not in session or not get_user_by_steamid(session['steamid']).get('is_admin'):
        return jsonify({'success': False, 'message': '无权访问'}), 403

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    try:
        with conn.cursor() as cursor:
            # 建议使用逻辑删除，而不是物理删除
            # sql = "DELETE FROM gift_packages WHERE id = %s"
            sql = "UPDATE gift_packages SET is_active = 0 WHERE id = %s"
            cursor.execute(sql, (package_id,))
        conn.commit()
        return jsonify({'success': True, 'message': '礼包已禁用'})
    except Exception as e:
        conn.rollback()
        logger.error(f"删除礼包失败: {e}")
        return jsonify({'success': False, 'message': f'删除失败: {e}'}), 500
    finally:
        if conn:
            conn.close()


# --- 前端用户API ---

@gift_packages_bp.route('/api/gift_packages', methods=['GET'])
def get_available_gift_packages():
    """获取当前用户可领取的礼包列表"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    steamid = session['steamid']
    user = get_user_by_steamid(steamid)
    if not user:
        return jsonify({'success': False, 'message': '无法获取用户信息'}), 400

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    try:
        with conn.cursor() as cursor:
            # 1. 获取所有已启用的礼包
            sql = "SELECT * FROM gift_packages WHERE is_active = 1"
            cursor.execute(sql)
            all_packages = cursor.fetchall()

            # 2. 获取用户所有领取记录
            sql = "SELECT package_id, MAX(claimed_at) as last_claimed_at FROM gift_claim_logs WHERE steamid = %s GROUP BY package_id"
            cursor.execute(sql, (steamid,))
            claimed_logs = {row['package_id']: row['last_claimed_at'] for row in cursor.fetchall()}

        available_packages = []
        user_vip_level = user.get('vip_level', 0)

        for package in all_packages:
            package_id = package['id']
            package_type = package['type']
            
            # 将items字段从JSON字符串转换为Python对象
            if isinstance(package.get('items'), str):
                package['items'] = json.loads(package['items'])

            # 检查领取资格
            can_claim = False
            reason = ''

            if package_type == 'VIP':
                required_level = package['vip_level_required']
                if user_vip_level >= required_level:
                    if package_id not in claimed_logs:
                        can_claim = True
                    else:
                        reason = '已领取'
                else:
                    reason = f'需要VIP {required_level}级'
            
            elif package_type == 'DAILY':
                if package_id in claimed_logs:
                    last_claimed_date = claimed_logs[package_id].date()
                    if last_claimed_date < date.today():
                        can_claim = True
                    else:
                        reason = '今日已领'
                else:
                    can_claim = True # 从未领过

            package['can_claim'] = can_claim
            package['reason'] = reason
            available_packages.append(package)

        return jsonify({'success': True, 'packages': available_packages})

    except Exception as e:
        logger.error(f"获取可用礼包列表失败: {e}")
        return jsonify({'success': False, 'message': f'获取失败: {e}'}), 500
    finally:
        if conn:
            conn.close()


@gift_packages_bp.route('/api/gift_packages/claim', methods=['POST'])
def claim_gift_package():
    """领取礼包"""
    if 'steamid' not in session:
        return jsonify({'success': False, 'message': '请先登录'}), 401

    steamid = session['steamid']
    data = request.json
    package_id = data.get('package_id')

    if not package_id:
        return jsonify({'success': False, 'message': '无效的请求'}), 400

    conn = get_db_connection()
    if not conn:
        return jsonify({'success': False, 'message': '数据库连接失败'}), 500

    try:
        # 开始事务
        conn.begin()

        # 1. 获取礼包信息和用户信息
        with conn.cursor() as cursor:
            sql = "SELECT * FROM gift_packages WHERE id = %s AND is_active = 1"
            cursor.execute(sql, (package_id,))
            package = cursor.fetchone()

        user = get_user_by_steamid(steamid, use_cache=False) # 获取最新信息

        if not package:
            return jsonify({'success': False, 'message': '礼包不存在或已下架'}), 404
        if not user:
            return jsonify({'success': False, 'message': '无法获取用户信息'}), 400

        # 2. 再次验证领取资格
        with conn.cursor() as cursor:
            if package['type'] == 'VIP':
                if user.get('vip_level', 0) < package['vip_level_required']:
                    return jsonify({'success': False, 'message': f"需要达到VIP {package['vip_level_required']}级才能领取"}), 403
                
                sql = "SELECT id FROM gift_claim_logs WHERE steamid = %s AND package_id = %s"
                cursor.execute(sql, (steamid, package_id))
                if cursor.fetchone():
                    return jsonify({'success': False, 'message': '您已经领取过此VIP礼包'}), 403

            elif package['type'] == 'DAILY':
                sql = "SELECT MAX(claimed_at) as last_claimed_at FROM gift_claim_logs WHERE steamid = %s AND package_id = %s"
                cursor.execute(sql, (steamid, package_id))
                log = cursor.fetchone()
                if log and log.get('last_claimed_at') and log['last_claimed_at'].date() >= date.today():
                    return jsonify({'success': False, 'message': '今日已领取，请明天再来'}), 403

        # 3. 发放物品到用户仓库
        items_to_add = json.loads(package['items']) if isinstance(package['items'], str) else package['items']
        now = datetime.now()
        
        with conn.cursor() as cursor:
            for item in items_to_add:
                sql = """
                INSERT INTO user_inventory (steamid, item_name, item_id, quantity, purchase_date, used)
                VALUES (%s, %s, %s, %s, %s, 0)
                """
                cursor.execute(sql, (steamid, item['name'], item['item_id'], item['quantity'], now))

        # 4. 记录领取日志
        with conn.cursor() as cursor:
            sql = "INSERT INTO gift_claim_logs (steamid, package_id, claimed_at) VALUES (%s, %s, %s)"
            cursor.execute(sql, (steamid, package_id, now))

        # 提交事务
        conn.commit()

        # 清除用户缓存，确保前端能获取到最新信息
        clear_user_cache(steamid)

        return jsonify({
            'success': True,
            'message': f"成功领取礼包: {package['name']}",
            'claimed_items': items_to_add
        })

    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"领取礼包失败: {e}")
        return jsonify({'success': False, 'message': f'操作失败: {e}'}), 500
    finally:
        if conn:
            conn.close()
