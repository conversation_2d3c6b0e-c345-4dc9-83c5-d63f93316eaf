{% extends "base.html" %}

{% block title %}VIP会员 - 7788商城{% endblock %}

{% block content %}
<div class="vip-container">
    <div class="vip-header">
        <h1><i class="fas fa-crown"></i> VIP会员系统</h1>
        <p>升级VIP享受更多特权，提升游戏体验</p>
    </div>

    <div class="vip-benefits">
        <div class="benefit-card">
            <div class="benefit-icon">
                <i class="fas fa-percentage"></i>
            </div>
            <div class="benefit-info">
                <h3>商品折扣</h3>
                <p>VIP会员购买商品享受折扣，等级越高折扣越大</p>
            </div>
        </div>
        <div class="benefit-card">
            <div class="benefit-icon">
                <i class="fas fa-coins"></i>
            </div>
            <div class="benefit-info">
                <h3>积分加成</h3>
                <p>VIP会员获取积分时享受加成，等级越高加成越多</p>
            </div>
        </div>
        <div class="benefit-card">
            <div class="benefit-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="benefit-info">
                <h3>签到奖励</h3>
                <p>VIP会员每日签到获得更多积分，等级越高奖励越多</p>
            </div>
        </div>
    </div>

    <div class="vip-levels-section">
        <h2>VIP等级与特权</h2>
        <div class="vip-levels-container">
            {% for level in vip_levels %}
            <div class="vip-level-card {% if user and user.vip_level >= level.level %}owned{% elif user and user.vip_level == level.level - 1 %}next-level{% endif %}">
                <div class="level-header">
                    <h3>VIP {{ level.level }}</h3>
                    {% if user and user.vip_level >= level.level %}
                    <span class="level-status owned"><i class="fas fa-check-circle"></i> 已拥有</span>
                    {% elif user and user.vip_level == level.level - 1 %}
                    <span class="level-status next"><i class="fas fa-arrow-circle-up"></i> 下一级</span>
                    {% endif %}
                </div>
                <div class="level-price">
                    <span class="price-value">{{ level.points_required }}</span>
                    <span class="price-label">积分</span>
                </div>
                <div class="level-benefits">
                    <div class="benefit-item">
                        <i class="fas fa-percentage"></i>
                        <span>商品{{ (level.discount_rate * 10)|int }}折优惠</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-coins"></i>
                        <span>积分{{ (level.points_bonus_rate * 100 - 100)|int }}%加成</span>
                    </div>
                    <div class="benefit-item">
                        <i class="fas fa-calendar-check"></i>
                        <span>签到奖励{{ level.checkin_bonus }}积分</span>
                    </div>
                </div>
                <div class="level-action">
                    {% if user %}
                        {% if user.vip_level >= level.level %}
                        <button class="vip-btn owned" disabled>已拥有</button>
                        {% elif user.vip_level == level.level - 1 %}
                        <button class="vip-btn upgrade" data-level="{{ level.level }}" data-points="{{ level.points_required }}">立即升级</button>
                        {% else %}
                        <button class="vip-btn locked" disabled>需先购买前置等级</button>
                        {% endif %}
                    {% else %}
                    <button class="vip-btn locked" disabled>请先登录</button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- VIP 专属礼包 -->
    <div class="vip-section">
        <h2 class="section-title"><i class="fas fa-gift"></i> VIP 专属礼包</h2>
        <div id="vip-packages-container" class="packages-grid">
            <!-- VIP礼包将通过JS动态加载 -->
        </div>
    </div>

    <div class="vip-faq">
        <h2>常见问题</h2>
        <div class="faq-item">
            <div class="faq-question">
                <i class="fas fa-question-circle"></i>
                <h3>如何升级VIP等级？</h3>
            </div>
            <div class="faq-answer">
                <p>您需要使用积分来升级VIP等级。每个等级都有相应的积分要求，您需要按顺序升级，不能跳级。</p>
            </div>
        </div>
        <div class="faq-item">
            <div class="faq-question">
                <i class="fas fa-question-circle"></i>
                <h3>VIP等级有什么好处？</h3>
            </div>
            <div class="faq-answer">
                <p>VIP会员可以享受商品折扣、积分加成和签到奖励等特权。等级越高，特权越多。</p>
            </div>
        </div>
        <div class="faq-item">
            <div class="faq-question">
                <i class="fas fa-question-circle"></i>
                <h3>VIP等级是永久的吗？</h3>
            </div>
            <div class="faq-answer">
                <p>是的，一旦您升级到某个VIP等级，您将永久拥有该等级及其特权。</p>
            </div>
        </div>
    </div>
</div>

<!-- 确认购买模态框 -->
<div class="modal" id="confirmVipModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>确认升级VIP</h2>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <p>您确定要升级到 <span id="confirmVipLevel"></span> 吗？</p>
            <p>需要消费 <span id="confirmVipPoints"></span> 积分</p>
            <p>当前积分: <span id="currentPoints">{{ user.points if user else 0 }}</span></p>
        </div>
        <div class="modal-footer">
            <button id="confirmVipBtn" class="primary-btn">确认升级</button>
            <button class="cancel-btn close-modal">取消</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取模态框元素
    const confirmVipModal = document.getElementById('confirmVipModal');
    const confirmVipLevel = document.getElementById('confirmVipLevel');
    const confirmVipPoints = document.getElementById('confirmVipPoints');
    const confirmVipBtn = document.getElementById('confirmVipBtn');
    const closeButtons = document.querySelectorAll('.close, .close-modal');
    
    // 获取所有升级按钮
    const upgradeButtons = document.querySelectorAll('.vip-btn.upgrade');
    
    // 为升级按钮添加点击事件
    upgradeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const level = this.getAttribute('data-level');
            const points = this.getAttribute('data-points');
            
            confirmVipLevel.textContent = `VIP${level}`;
            confirmVipPoints.textContent = points;
            
            // 显示模态框
            confirmVipModal.style.display = 'flex';
            
            // 设置确认按钮的数据
            confirmVipBtn.setAttribute('data-level', level);
        });
    });
    
    // 为确认按钮添加点击事件
    confirmVipBtn.addEventListener('click', function() {
        const level = parseInt(this.getAttribute('data-level'));
        
        // 发送升级请求
        fetch('/upgrade_vip', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                level: level
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示成功消息
                customAlert(data.message);
                // 刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                // 显示错误消息
                customAlert(data.message);
            }
            
            // 关闭模态框
            confirmVipModal.style.display = 'none';
        })
        .catch(error => {
            console.error('升级VIP失败:', error);
            customAlert('网络错误，请稍后重试');
            confirmVipModal.style.display = 'none';
        });
    });
    
    // 为关闭按钮添加点击事件
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            confirmVipModal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭模态框
    window.addEventListener('click', function(event) {
        if (event.target === confirmVipModal) {
            confirmVipModal.style.display = 'none';
        }
    });

    // 加载VIP礼包
    const packagesContainer = document.getElementById('vip-packages-container');
    fetch('/api/gift_packages')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const vipPackages = data.packages.filter(p => p.type === 'VIP');
                if (vipPackages.length === 0) {
                    packagesContainer.innerHTML = '<p>暂无VIP礼包</p>';
                    return;
                }
                
                packagesContainer.innerHTML = ''; // 清空容器
                vipPackages.forEach(pkg => {
                    const itemsHtml = pkg.items.map(item => 
                        `<div class="item-tooltip" data-item-name="${item.name}" data-item-quantity="${item.quantity}">
                            <img src="${item.image_path || '/static/images/items/default.png'}" alt="${item.name}">
                            <span class="item-quantity-badge">x${item.quantity}</span>
                         </div>`
                    ).join('');

                    const packageCard = `
                        <div class="package-card ${pkg.can_claim ? '' : 'claimed'}">
                            <div class="package-header">
                                <h3>${pkg.name}</h3>
                                <span class="vip-level-badge">VIP ${pkg.vip_level_required}+</span>
                            </div>
                            <div class="package-body">
                                <div class="package-image">
                                    <img src="${pkg.image_path || '/static/images/items/default.png'}" alt="${pkg.name}">
                                </div>
                                <div class="package-items">
                                    ${itemsHtml}
                                </div>
                            </div>
                            <div class="package-footer">
                                <button class="claim-btn" data-package-id="${pkg.id}" ${pkg.can_claim ? '' : 'disabled'}>
                                    ${pkg.can_claim ? '<i class="fas fa-check"></i> 领取' : `<i class="fas fa-lock"></i> ${pkg.reason}`}
                                </button>
                            </div>
                        </div>
                    `;
                    packagesContainer.insertAdjacentHTML('beforeend', packageCard);
                });

                // 添加领取按钮事件
                document.querySelectorAll('.claim-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const packageId = this.dataset.packageId;
                        claimPackage(packageId, this);
                    });
                });
            } else {
                packagesContainer.innerHTML = `<p>加载礼包失败: ${data.message}</p>`;
            }
        })
        .catch(error => {
            console.error('获取VIP礼包失败:', error);
            packagesContainer.innerHTML = '<p>加载礼包时发生网络错误。</p>';
        });

    function claimPackage(packageId, button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';

        fetch('/api/gift_packages/claim', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ package_id: packageId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('礼包领取成功！物品已发往您的仓库。');
                button.innerHTML = '<i class="fas fa-lock"></i> 已领取';
                // 可选：刷新页面或局部更新状态
                window.location.reload();
            } else {
                alert(`领取失败: ${data.message}`);
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-check"></i> 领取';
            }
        })
        .catch(error => {
            console.error('领取礼包请求失败:', error);
            alert('领取失败，发生网络错误。');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-check"></i> 领取';
        });
    }
});
</script>
{% endblock %}