{% extends "base.html" %}
{% block title %}个人信息 - SCUM物品代码网站{% endblock %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center mb-4">
            <h1>个人中心</h1>
            <p class="text-muted">您的游戏数据和个人信息</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="user-info-card">
                <div class="user-info-card-content">
                    <div class="user-info-card-username">
                        {{ user.username if user else '用户名' }}
                    </div>
                    <div class="text-muted small mb-3">加入于 2024年5月</div>

                    <div class="user-info-card-stats">
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">积分</div>
                            <div class="user-info-card-stat-value">{{ user.points if user else '0' }}</div>
                        </div>
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">击杀数</div>
                            <div class="user-info-card-stat-value">{{ user.kills if user else '0' }}</div>
                        </div>
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">死亡数</div>
                            <div class="user-info-card-stat-value">{{ user.deaths if user else '0' }}</div>
                        </div>
                        <div class="user-info-card-stat">
                            <div class="user-info-card-stat-label">KD比例</div>
                            <div class="user-info-card-stat-value">{{ user.kd|round(2) if user and user.kd else '0' }}</div>
                        </div>
                    </div>

                    {% if user and user.is_vip %}
                    <div class="user-info-card-vip">
                        <i class="fas fa-crown"></i> VIP用户
                    </div>
                    {% endif %}

                    {% if user and user.xyz %}
                    <div class="user-info-card-coordinate">
                        <div class="small mb-1">回家坐标</div>
                        <div>{{ user.xyz }}</div>
                    </div>
                    {% else %}
                    <div class="user-info-card-coordinate">
                        <div class="small mb-1">回家坐标</div>
                        <div>未设置</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 每日礼包部分 -->
    <div class="row justify-content-center mt-4">
        <div class="col-md-6">
            <div class="card bg-dark text-white">
                <div class="card-header">
                    <i class="fas fa-gift"></i> 每日礼包
                </div>
                <div class="card-body" id="daily-package-container">
                    <!-- 每日礼包将通过JS动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 收藏物品部分 -->
    <div class="row justify-content-center mt-4">
        <div class="col-md-6">
            <div class="card bg-dark text-white">
                <div class="card-header">
                    <i class="fas fa-heart"></i> 收藏物品
                </div>
                <div class="card-body">
                    {% if user and user.favorites %}
                    <div class="row">
                        {% for item in user.favorites %}
                        <div class="col-md-6 mb-3">
                            <div class="p-2 bg-secondary rounded">
                                <div class="small text-white">{{ item.name }}</div>
                                <div class="text-light small">{{ item.code }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-center text-muted my-4">
                        <i class="fas fa-info-circle"></i> 您还没有收藏任何物品
                    </p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dailyPackageContainer = document.getElementById('daily-package-container');

    // 加载每日礼包
    fetch('/api/gift_packages')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dailyPackage = data.packages.find(p => p.type === 'DAILY');
                if (!dailyPackage) {
                    dailyPackageContainer.innerHTML = '<p class="text-center">今日暂无每日礼包</p>';
                    return;
                }

                const itemsHtml = dailyPackage.items.map(item => 
                    `<div class="item-tooltip" data-item-name="${item.name}" data-item-quantity="${item.quantity}">
                        <img src="${item.image_path || '/static/images/items/default.png'}" alt="${item.name}">
                        <span class="item-quantity-badge">x${item.quantity}</span>
                     </div>`
                ).join('');

                const packageHtml = `
                    <div class="daily-package-card">
                        <div class="package-info">
                            <h4>${dailyPackage.name}</h4>
                            <p>${dailyPackage.description || '每日登录即可领取好礼！'}</p>
                            <div class="package-items-preview">${itemsHtml}</div>
                        </div>
                        <div class="package-action">
                            <button id="claim-daily-btn" class="btn btn-primary btn-lg" ${dailyPackage.can_claim ? '' : 'disabled'}>
                                ${dailyPackage.can_claim ? '<i class="fas fa-gift"></i> 立即领取' : `<i class="fas fa-check-circle"></i> ${dailyPackage.reason}`}
                            </button>
                        </div>
                    </div>
                `;
                dailyPackageContainer.innerHTML = packageHtml;

                // 绑定领取按钮事件
                const claimBtn = document.getElementById('claim-daily-btn');
                if (claimBtn && dailyPackage.can_claim) {
                    claimBtn.addEventListener('click', function() {
                        claimPackage(dailyPackage.id, this);
                    });
                }
            } else {
                dailyPackageContainer.innerHTML = `<p class="text-center text-danger">加载每日礼包失败: ${data.message}</p>`;
            }
        })
        .catch(error => {
            console.error('获取每日礼包失败:', error);
            dailyPackageContainer.innerHTML = '<p class="text-center text-danger">加载礼包时发生网络错误。</p>';
        });

    function claimPackage(packageId, button) {
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在领取...';

        fetch('/api/gift_packages/claim', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ package_id: packageId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('礼包领取成功！物品已发往您的仓库。');
                button.innerHTML = '<i class="fas fa-check-circle"></i> 今日已领';
            } else {
                alert(`领取失败: ${data.message}`);
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-gift"></i> 立即领取';
            }
        })
        .catch(error => {
            console.error('领取礼包请求失败:', error);
            alert('领取失败，发生网络错误。');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-gift"></i> 立即领取';
        });
    }
});
</script>
{% endblock %}
