<!DOCTYPE html>
<html lang="zh-CN" class="dark-theme">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}7788商城{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/bootstrap/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/entertainment.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sponsor.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/product-card.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/sponsor_vip.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/user-vip-badge.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/vip-purchase.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/data.css') }}">
    <style>
        /* Flash消息样式 */
        .flash-messages {
            margin: 10px 0;
            padding: 0;
            list-style: none;
        }

        .flash-message {
            background-color: #4CAF50;
            color: white;
            padding: 12px 20px;
            margin-bottom: 10px;
            border-radius: 4px;
            position: relative;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            animation: fadeIn 0.3s ease-in-out;
        }

        .flash-close {
            position: absolute;
            right: 10px;
            top: 10px;
            cursor: pointer;
            font-size: 18px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <!-- 预加载默认图片 -->
    <link rel="preload" href="/static/uploads/default-item.jpg" as="image">

    <!-- 全局图片缓存和预加载脚本 -->
    <script>
        // 创建全局图片缓存
        window.imageCache = {
            cache: {},
            pendingRequests: {},

            // 获取图片，如果缓存中有则直接返回，否则加载
            get: function(src) {
                // 如果是相对路径，转换为绝对路径
                if (src.startsWith('/')) {
                    src = window.location.origin + src;
                }

                // 如果已经缓存，直接返回
                if (this.cache[src]) {
                    return this.cache[src];
                }

                // 如果正在加载，返回默认图片
                if (this.pendingRequests[src]) {
                    return this.cache['/static/uploads/default-item.jpg'] || null;
                }

                // 开始加载图片
                this.pendingRequests[src] = true;
                const img = new Image();
                const self = this;

                img.onload = function() {
                    self.cache[src] = img;
                    delete self.pendingRequests[src];
                    // 触发自定义事件，通知图片加载完成
                    document.dispatchEvent(new CustomEvent('imageCached', { detail: { src: src } }));
                };

                img.onerror = function() {
                    delete self.pendingRequests[src];
                    // 如果加载失败且不是默认图片，使用默认图片
                    if (src !== '/static/uploads/default-item.jpg' && self.cache['/static/uploads/default-item.jpg']) {
                        return self.cache['/static/uploads/default-item.jpg'];
                    }
                };

                img.src = src;
                return null;
            },

            // 预加载图片
            preload: function(src) {
                this.get(src);
            },

            // 预加载多个图片
            preloadMultiple: function(sources) {
                for (let i = 0; i < sources.length; i++) {
                    this.preload(sources[i]);
                }
            }
        };

        // 预加载默认图片
        document.addEventListener('DOMContentLoaded', function() {
            // 注册Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/static/js/service-worker.js')
                    .then(registration => {
                        console.log('Service Worker 注册成功，范围:', registration.scope);
                    })
                    .catch(error => {
                        console.error('Service Worker 注册失败:', error);
                    });
            }

            // 预加载默认图片
            imageCache.preload('/static/uploads/default-item.jpg');

            // 重写Image对象的加载行为
            const originalImageProto = Image.prototype;
            const originalSetAttribute = Element.prototype.setAttribute;

            // 重写setAttribute方法，拦截对src和onerror的设置
            Element.prototype.setAttribute = function(name, value) {
                if (this.tagName === 'IMG') {
                    if (name === 'src') {
                        // 尝试从缓存中获取图片
                        const cachedImg = imageCache.get(value);
                        if (cachedImg) {
                            // 如果图片已经缓存，使用缓存的图片数据
                            originalSetAttribute.call(this, name, cachedImg.src);
                            return;
                        }
                    } else if (name === 'onerror' && value.includes('default-item.jpg')) {
                        // 不设置onerror，而是在src加载失败时手动处理
                        this.addEventListener('error', function(e) {
                            if (!this.src.includes('default-item.jpg')) {
                                this.src = '/static/uploads/default-item.jpg';
                            }
                            e.stopPropagation(); // 阻止事件冒泡
                        }, {once: true}); // 只触发一次
                        return;
                    }
                }

                // 对于其他情况，使用原始方法
                originalSetAttribute.call(this, name, value);
            };

            // 重写图片加载错误处理
            const originalImageSrcSetter = Object.getOwnPropertyDescriptor(HTMLImageElement.prototype, 'src').set;
            Object.defineProperty(HTMLImageElement.prototype, 'src', {
                set: function(value) {
                    // 尝试从缓存中获取图片
                    const cachedImg = imageCache.get(value);
                    if (cachedImg) {
                        // 如果图片已经缓存，使用缓存的图片数据
                        originalImageSrcSetter.call(this, cachedImg.src);
                    } else {
                        // 如果图片没有缓存，正常加载
                        originalImageSrcSetter.call(this, value);

                        // 添加错误处理
                        if (!this.hasAttribute('data-error-handled')) {
                            this.setAttribute('data-error-handled', 'true');
                            this.addEventListener('error', function(e) {
                                if (!this.src.includes('default-item.jpg')) {
                                    this.src = '/static/uploads/default-item.jpg';
                                }
                            }, {once: true});
                        }
                    }
                },
                get: Object.getOwnPropertyDescriptor(HTMLImageElement.prototype, 'src').get
            });

            // 预加载页面上的所有图片
            setTimeout(function() {
                const allImages = document.querySelectorAll('img');
                const imageSources = [];

                allImages.forEach(function(img) {
                    if (img.src && !img.src.includes('data:')) {
                        imageSources.push(img.src);
                    }
                });

                // 预加载所有图片
                imageCache.preloadMultiple(imageSources);
            }, 1000); // 等待页面加载完成
        });
    </script>

    {% block head_extra %}{% endblock %}
</head>
<body class="dark-theme">
    <!-- 导航栏 -->
    <nav class="main-nav">
        <div class="container nav-container">
            <div class="logo">
                <a href="/">
                    <i class="fas fa-gamepad"></i>
                    <span>7788商城</span>
                </a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item"><a href="/home" class="nav-link {% if active_page == 'home' %}active{% endif %}"><i class="fas fa-home"></i> 主页</a></li>
                <li class="nav-item"><a href="/items" class="nav-link {% if active_page == 'items' %}active{% endif %}"><i class="fas fa-cube"></i> 物品代码</a></li>
                <li class="nav-item"><a href="/sponsor" class="nav-link {% if active_page == 'sponsor' %}active{% endif %}"><i class="fas fa-heart"></i> 赞助</a></li>
                <li class="nav-item"><a href="/data" class="nav-link {% if active_page == 'data' %}active{% endif %}"><i class="fas fa-chart-bar"></i> 数据</a></li>
                <li class="nav-item"><a href="/entertainment" class="nav-link {% if active_page == 'entertainment' %}active{% endif %}"><i class="fas fa-dice"></i> 娱乐</a></li>
                <li class="nav-item"><a href="/battlepass" class="nav-link {% if active_page == 'battlepass' %}active{% endif %}"><i class="fas fa-trophy"></i> 通行证</a></li>
                <li class="nav-item"><a href="/recycle" class="nav-link {% if active_page == 'recycle' %}active{% endif %}"><i class="fas fa-recycle"></i> 回收</a></li>
                <li class="nav-item"><a href="/profile" class="nav-link {% if active_page == 'profile' %}active{% endif %}"><i class="fas fa-user"></i> 个人信息</a></li>
                {% if session.username %}
                <li class="nav-item"><a href="/inventory" class="nav-link {% if active_page == 'inventory' %}active{% endif %}"><i class="fas fa-warehouse"></i> 我的仓库</a></li>
                {% endif %}
                {% if user and user.get('is_admin') %}
                <li class="nav-item"><a href="{{ url_for('admin_panel') }}" class="nav-link {% if active_page == 'admin' %}active{% endif %}"><i class="fas fa-shield-alt"></i> 管理员</a></li>
                {% endif %}
            </ul>
            <div class="nav-right">
                {% if session.username %}
                <div class="nav-points">
                    <i class="fas fa-coins"></i>
                    <span>{{ user.points if user else '0' }} 积分</span>
                </div>
                {% endif %}
                <div class="auth-links">
                    {% if session.username %}
                    <div class="user-dropdown">
                        <button class="dropdown-toggle">
                            <i class="fas fa-user"></i>
                            {{ session.username }}
                            {% if user and user.get('vip_level', 0) > 0 %}
                            <span class="user-vip-badge"><i class="fas fa-crown"></i> VIP{{ user.get('vip_level', 0) }}</span>
                            {% endif %}
                        </button>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('profile') }}"><i class="fas fa-user"></i> 个人信息</a>
                            <a href="{{ url_for('sponsor') }}"><i class="fas fa-crown"></i> VIP会员</a>
                            {% if user and user.get('is_admin') %}
                            <a href="{{ url_for('admin_panel') }}"><i class="fas fa-shield-alt"></i> 管理员</a>
                            {% endif %}
                            <a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                        </div>
                    </div>
                    {% else %}
                    <a href="{{ url_for('login') }}" class="auth-button login-btn"><i class="fas fa-sign-in-alt"></i> 登录</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- 主内容区 -->
    <main class="container">
        <!-- Flash消息显示 -->
        {% with messages = get_flashed_messages() %}
            {% if messages %}
                <div class="flash-messages">
                    {% for message in messages %}
                        <div class="flash-message">
                            {{ message }}
                            <span class="flash-close">&times;</span>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- 复制成功提示 -->
    <div class="copy-tooltip" id="copyTooltip">已复制到剪贴板<br>请前往游戏内指令框输入</div>

    <!-- 图片放大模态框 -->
    <div class="image-modal" id="imageModal">
        <span class="modal-close">&times;</span>
        <img class="modal-image" id="modalImage">
        <div class="modal-caption" id="modalCaption"></div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="custom-confirm" class="custom-dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <span class="dialog-title"><i class="fas fa-exclamation-circle"></i> 确认操作</span>
                <span class="dialog-close">&times;</span>
            </div>
            <div class="dialog-body">
                <p id="dialog-message"></p>
            </div>
            <div class="dialog-footer">
                <button id="dialog-cancel" class="btn-cancel"><i class="fas fa-times"></i> 取消</button>
                <button id="dialog-confirm" class="btn-confirm"><i class="fas fa-check"></i> 确定</button>
            </div>
        </div>
    </div>

    <!-- 自定义提示对话框 -->
    <div id="custom-alert" class="custom-dialog">
        <div class="dialog-content">
            <div class="dialog-header">
                <span class="dialog-title"><i class="fas fa-info-circle"></i> 提示信息</span>
                <span class="dialog-close">&times;</span>
            </div>
            <div class="dialog-body">
                <p id="alert-message"></p>
            </div>
            <div class="dialog-footer">
                <button id="alert-confirm" class="btn-confirm"><i class="fas fa-check"></i> 确定</button>
            </div>
        </div>
    </div>

    <!-- 浮动购物车 -->
    {% if user %}
    <div class="floating-cart" id="floatingCart">
        <div class="cart-collapsed" id="cartCollapsed">
            <i class="fas fa-shopping-cart cart-icon"></i>
            <span class="cart-badge" id="cartBadge">0</span>
        </div>
        <div class="cart-expanded" id="cartExpanded">
            <div class="cart-header">
                <div class="cart-title">
                    <i class="fas fa-shopping-cart"></i> 购物车
                </div>
                <div class="cart-close" id="cartClose">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="cart-items" id="cartItems">
                <div class="cart-empty" id="cartEmpty">
                    <i class="fas fa-shopping-basket"></i>
                    <p>购物车为空</p>
                </div>
                <!-- 购物车物品将通过JavaScript动态添加 -->
            </div>
            <div class="cart-footer">
                <div class="cart-total">
                    <span>总计：</span>
                    <span class="cart-total-price" id="cartTotalPrice">0 积分</span>
                </div>
                <div class="cart-actions">
                    <button class="cart-clear" id="cartClear">清空</button>
                    <button class="cart-checkout" id="cartCheckout">结算</button>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 页脚 -->
    <footer class="main-footer">
        <div class="container">
            <div class="footer-bottom">
                <p>&copy; 2025 出其东门 | 保留所有权利</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery-3.5.1.min.js') }}"></script>
    <script src="{{ url_for('static', filename='vendor/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/dialogs.js') }}"></script>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Flash消息关闭功能
        const flashCloseButtons = document.querySelectorAll('.flash-close');
        if (flashCloseButtons.length > 0) {
            flashCloseButtons.forEach(btn => {
                btn.addEventListener('click', function() {
                    const flashMessage = this.parentElement;
                    flashMessage.style.opacity = '0';
                    flashMessage.style.height = '0';
                    flashMessage.style.padding = '0';
                    flashMessage.style.margin = '0';
                    setTimeout(() => {
                        flashMessage.remove();
                    }, 300);
                });
            });

            // 5秒后自动关闭Flash消息
            setTimeout(() => {
                flashCloseButtons.forEach(btn => {
                    const flashMessage = btn.parentElement;
                    flashMessage.style.opacity = '0';
                    flashMessage.style.height = '0';
                    flashMessage.style.padding = '0';
                    flashMessage.style.margin = '0';
                    setTimeout(() => {
                        if (flashMessage.parentElement) {
                            flashMessage.remove();
                        }
                    }, 300);
                });
            }, 5000);
        }

        // 用户下拉菜单
        const userDropdown = document.querySelector('.user-dropdown');
        if (userDropdown) {
            const dropdownToggle = userDropdown.querySelector('.dropdown-toggle');
            const dropdownMenu = userDropdown.querySelector('.dropdown-menu');

            dropdownToggle.addEventListener('click', function() {
                dropdownMenu.classList.toggle('show');
            });

            document.addEventListener('click', function(e) {
                if (!userDropdown.contains(e.target)) {
                    dropdownMenu.classList.remove('show');
                }
            });
        }

        // 复制功能
        const copyButtons = document.querySelectorAll('.copy-btn');
        const copyTooltip = document.getElementById('copyTooltip');

        if (copyButtons.length > 0 && copyTooltip) {
            copyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const textToCopy = this.getAttribute('data-name');

                    // 创建临时文本区域
                    const textArea = document.createElement('textarea');
                    textArea.value = textToCopy;
                    document.body.appendChild(textArea);
                    textArea.select();

                    // 复制文本
                    document.execCommand('copy');

                    // 移除临时元素
                    document.body.removeChild(textArea);

                    // 显示提示
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.top = (this.getBoundingClientRect().top - 60) + 'px';
                    copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

                    // 3秒后隐藏提示
                    setTimeout(function() {
                        copyTooltip.style.opacity = '0';
                    }, 2000);
                });
            });
        }

        // 图片放大功能
        const productImages = document.querySelectorAll('.products-grid .item-image'); // 只选择商品网格中的图片
        const zoomIcons = document.querySelectorAll('.image-zoom-icon');
        const imageModal = document.getElementById('imageModal');

        if (imageModal) {
            const modalImage = document.getElementById('modalImage');
            const modalCaption = document.getElementById('modalCaption');
            const modalClose = document.querySelector('.modal-close');

            function openImageModal(imgSrc, caption) {
                modalImage.src = imgSrc;
                modalCaption.textContent = caption;
                imageModal.style.display = 'flex';
                document.body.style.overflow = 'hidden'; // 防止背景滚动
            }

            function closeImageModal() {
                imageModal.style.display = 'none';
                document.body.style.overflow = 'auto'; // 恢复背景滚动
            }

            // 点击商品图片打开模态框
            if (productImages.length > 0) {
                productImages.forEach(img => {
                    img.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止冒泡，避免触发卡片的点击事件
                        openImageModal(this.src, this.alt);
                    });
                });
            }

            // 点击放大图标打开模态框
            if (zoomIcons.length > 0) {
                zoomIcons.forEach(icon => {
                    icon.addEventListener('click', function(e) {
                        e.stopPropagation();
                        const img = this.parentElement.querySelector('.item-image');
                        openImageModal(img.src, img.alt);
                    });
                });
            }

            // 关闭模态框
            if (modalClose) {
                modalClose.addEventListener('click', closeImageModal);
            }

            // 点击模态框背景关闭
            imageModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeImageModal();
                }
            });

            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && imageModal.style.display === 'flex') {
                    closeImageModal();
                }
            });
        }
    });
    </script>

    <!-- 购物车功能 -->
    {% if user %}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 购物车功能
        const cart = {
            items: [],

            // 从 localStorage 加载购物车
            load: function() {
                const savedCart = localStorage.getItem('scumShopCart');
                if (savedCart) {
                    try {
                        this.items = JSON.parse(savedCart);
                        // 确保所有ID都是字符串类型
                        this.items = this.items.map(item => ({
                            ...item,
                            id: String(item.id)
                        }));
                        // 加载购物车成功
                    } catch (e) {
                        console.error('加载购物车失败:', e);
                        this.items = [];
                    }
                } else {
                    this.items = [];
                    // 购物车为空，初始化空数组
                }
            },

            // 保存购物车到 localStorage
            save: function() {
                // 确保所有ID都是字符串类型
                const itemsToSave = this.items.map(item => ({
                    ...item,
                    id: String(item.id)
                }));
                localStorage.setItem('scumShopCart', JSON.stringify(itemsToSave));
                // 保存购物车
            },

            // 添加商品到购物车
            addItem: function(id, name, price, original_price, quantity = 1) {
                // 使用物品名称和ID的组合作为唯一标识符
                const uniqueId = `${name}_${id}`;
                // 检查是否已存在相同唯一标识符的商品
                const existingItemIndex = this.items.findIndex(item => String(item.id) === uniqueId);

                if (existingItemIndex >= 0) {
                    this.items[existingItemIndex].quantity += quantity;
                } else {
                    const newItem = {
                        id: uniqueId, // 使用唯一标识符作为ID
                        name: name,
                        price: parseInt(price),
                        original_price: parseInt(original_price || price), // 保存原始价格
                        quantity: quantity,
                        originalId: id // 保存原始ID以便后续使用
                    };
                    this.items.push(newItem);
                }
                this.save();
                this.updateUI();
            },

            // 从购物车移除商品
            removeItem: function(id) {
                const itemId = String(id);
                this.items = this.items.filter(item => String(item.id) !== itemId);
                this.save();
                this.updateUI();
            },

            // 更新商品数量
            updateQuantity: function(id, quantity) {
                const itemId = String(id);
                const item = this.items.find(item => String(item.id) === itemId);
                if (item) {
                    item.quantity = Math.max(1, quantity);
                    this.save();
                    this.updateUI();
                }
            },

            // 清空购物车
            clear: function() {
                this.items = [];
                this.save();
                this.updateUI();
            },

            // 计算总价
            getTotal: function() {
                return this.items.reduce((total, item) => total + (item.price * item.quantity), 0);
            },

            // 获取商品总数
            getItemCount: function() {
                return this.items.reduce((count, item) => count + item.quantity, 0);
            },

            // 更新购物车UI
            updateUI: function() {
                const cartBadge = document.getElementById('cartBadge');
                const cartItems = document.getElementById('cartItems');
                const cartEmpty = document.getElementById('cartEmpty');
                const cartTotalPrice = document.getElementById('cartTotalPrice');

                // 更新UI

                // 更新购物车数量标记
                cartBadge.textContent = this.getItemCount();

                // 更新总价
                cartTotalPrice.textContent = `${this.getTotal()} 积分`;

                // 清空购物车列表
                while (cartItems.firstChild) {
                    if (cartItems.firstChild === cartEmpty) {
                        break;
                    }
                    cartItems.removeChild(cartItems.firstChild);
                }

                // 显示或隐藏空购物车提示
                if (this.items.length === 0) {
                    cartEmpty.style.display = 'block';
                } else {
                    cartEmpty.style.display = 'none';

                    // 添加商品到购物车列表
                    this.items.forEach((item, index) => {
                        const cartItem = document.createElement('div');
                        cartItem.className = 'cart-item';
                        cartItem.innerHTML = `
                            <div class="cart-item-info">
                                <div class="cart-item-name">${item.name}</div>
                                <div class="cart-item-price">${item.price} 积分</div>
                            </div>
                            <div class="cart-item-controls">
                                <button class="cart-quantity-btn decrease-btn" data-id="${item.id}">-</button>
                                <input type="number" class="cart-quantity" value="${item.quantity}" min="1" data-id="${item.id}">
                                <button class="cart-quantity-btn increase-btn" data-id="${item.id}">+</button>
                            </div>
                            <div class="cart-item-remove" data-id="${item.id}">
                                <i class="fas fa-times"></i>
                            </div>
                        `;

                        cartItems.insertBefore(cartItem, cartEmpty);
                        // 商品已添加到UI
                    });

                    // 添加事件监听器
                    const decreaseBtns = cartItems.querySelectorAll('.decrease-btn');
                    const increaseBtns = cartItems.querySelectorAll('.increase-btn');
                    const quantityInputs = cartItems.querySelectorAll('.cart-quantity');
                    const removeButtons = cartItems.querySelectorAll('.cart-item-remove');

                    decreaseBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = this.getAttribute('data-id');
                            const item = cart.items.find(item => String(item.id) === String(id));
                            if (item && item.quantity > 1) {
                                cart.updateQuantity(id, item.quantity - 1);
                            }
                        });
                    });

                    increaseBtns.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = this.getAttribute('data-id');
                            const item = cart.items.find(item => String(item.id) === String(id));
                            if (item) {
                                cart.updateQuantity(id, item.quantity + 1);
                            }
                        });
                    });

                    quantityInputs.forEach(input => {
                        input.addEventListener('change', function() {
                            const id = this.getAttribute('data-id');
                            const quantity = parseInt(this.value) || 1;
                            cart.updateQuantity(id, quantity);
                        });
                    });

                    removeButtons.forEach(btn => {
                        btn.addEventListener('click', function() {
                            const id = this.getAttribute('data-id');
                            cart.removeItem(id);
                        });
                    });
                }
            }
        };

        // 初始化购物车
        cart.load();
        cart.updateUI();

        // 购物车展开/收起功能
        const cartCollapsed = document.getElementById('cartCollapsed');
        const cartExpanded = document.getElementById('cartExpanded');
        const cartClose = document.getElementById('cartClose');

        cartCollapsed.addEventListener('click', function() {
            cartExpanded.style.display = 'flex';
            cartCollapsed.style.display = 'none';
        });

        cartClose.addEventListener('click', function() {
            cartExpanded.style.display = 'none';
            cartCollapsed.style.display = 'flex';
        });

        // 添加到购物车按钮
        const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

        if (addToCartButtons.length > 0) {
            addToCartButtons.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const id = this.getAttribute('data-id');
                    const name = this.getAttribute('data-name');
                    const price = this.getAttribute('data-price');
                    const originalPrice = this.getAttribute('data-original-price') || price;

                    cart.addItem(id, name, price, originalPrice);

                    // 添加动画效果
                    this.classList.add('added');
                    setTimeout(() => {
                        this.classList.remove('added');
                    }, 500);

                    // 显示提示
                    const copyTooltip = document.getElementById('copyTooltip');
                    copyTooltip.textContent = '已添加到购物车';
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.top = (this.getBoundingClientRect().top - 40) + 'px';
                    copyTooltip.style.left = (this.getBoundingClientRect().left + this.offsetWidth / 2) + 'px';

                    setTimeout(function() {
                        copyTooltip.style.opacity = '0';
                        copyTooltip.textContent = '已复制到剪贴板\n请前往游戏内指令框输入';
                    }, 1000);
                });
            });
        }

        // 清空购物车
        const cartClear = document.getElementById('cartClear');
        cartClear.addEventListener('click', function() {
            customConfirm('确定要清空购物车吗？', (confirmed) => {
                if (confirmed) {
                    cart.clear();
                }
            });
        });

        // 结算功能
        const cartCheckout = document.getElementById('cartCheckout');
        cartCheckout.addEventListener('click', function() {
            if (cart.items.length === 0) {
                customAlert('购物车为空，请先添加商品');
                return;
            }

            // 显示详细的结算信息
            let itemsList = cart.items.map(item => `${item.name} x ${item.quantity} (${item.price * item.quantity} 积分)`).join('<br>');
            let confirmMessage = `确定要结算以下物品吗？<br><br>${itemsList}<br><br>总计: <b>${cart.getTotal()} 积分</b>`;

            customConfirm(confirmMessage, (confirmed) => {
                if (confirmed) {
                    // 发送结算请求

                    // 发送结算请求到服务器
                    fetch('/buy_items', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: JSON.stringify({
                            items: cart.items.map(item => ({
                                id: item.originalId || item.id.split('_')[1], // 使用原始ID或从唯一ID中提取
                                name: item.name,
                                price: item.price,
                                original_price: item.original_price || item.price, // 添加原始价格
                                quantity: item.quantity
                            })),
                            total_price: cart.getTotal()
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新用户积分显示
                            const pointsDisplay = document.querySelector('.nav-points span');
                            if (pointsDisplay) {
                                pointsDisplay.textContent = data.user_points;
                            }

                            // 清空购物车
                            cart.clear();

                            // 显示成功提示
                            let successMessage = data.message || '购买成功！';
                            if (data.purchased_items && data.purchased_items.length > 0) {
                                successMessage += '<br><br>已购买的物品：<br>' +
                                    data.purchased_items.map(item => `${item.name} x ${item.quantity}`).join('<br>');
                            }
                            customAlert(successMessage);

                            // 关闭购物车
                            cartExpanded.style.display = 'none';
                            cartCollapsed.style.display = 'flex';
                        } else {
                            // 显示错误提示
                            customAlert(data.message || '购买失败，请稍后再试。');
                        }
                    })
                    .catch(error => {
                        console.error('结算请求失败:', error);
                        customAlert('网络错误，请稍后重试');
                    });
                }
            });
        });

        // ESC键关闭购物车
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && cartExpanded.style.display === 'flex') {
                cartExpanded.style.display = 'none';
                cartCollapsed.style.display = 'flex';
            }
        });
    });
    </script>
    {% endif %}

    {% block scripts %}{% endblock %}
</body>
</html>
