{% extends "base.html" %}

{% block title %}礼包管理 - 后台{% endblock %}

{% block head_extra %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
<style>
    .package-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
    }
    .package-table th, .package-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #333;
    }
    .package-table th { background-color: #222; color: #fff; }
    .package-table tr:hover { background-color: #2a2a2a; }
    .package-actions button { padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 14px; border: none; }
    .btn-edit { background-color: #2196F3; color: white; }
    .btn-delete { background-color: #F44336; color: white; }
    .package-status { display: inline-block; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
    .status-active { background-color: #4CAF50; color: white; }
    .status-inactive { background-color: #9E9E9E; color: white; }
    .package-image-preview { width: 50px; height: 50px; object-fit: cover; border-radius: 4px; }
    .items-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 15px;
        max-height: 500px;
        overflow-y: auto;
        padding: 15px;
        border: 1px solid #444;
        border-radius: 4px;
    }
    .item-card {
        background-color: #2a2a2a;
        border-radius: 8px;
        padding: 10px;
        cursor: pointer;
        transition: all 0.2s;
        position: relative;
    }
    .item-card:hover { background-color: #383838; }
    .item-card.selected { background-color: #1976D2; box-shadow: 0 0 10px #1976D2; }
    .item-card img { width: 100%; height: 120px; object-fit: cover; border-radius: 6px; margin-bottom: 10px; }
    .item-card .item-name { font-size: 14px; text-align: center; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
    .item-card .item-quantity {
        position: absolute;
        top: 5px;
        right: 5px;
        background-color: rgba(0,0,0,0.7);
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
    }
    .selected-items-preview { 
        display: flex; 
        flex-wrap: wrap; 
        gap: 10px; 
        margin-top: 15px; 
        padding: 10px; 
        background-color: #222; 
        border-radius: 4px; 
        min-height: 50px; 
    }
    .selected-item-tag { 
        background-color: #4CAF50; 
        color: white; 
        padding: 5px 10px; 
        border-radius: 15px; 
        display: flex; 
        align-items: center; 
        gap: 5px; 
    }
    .selected-item-tag .remove-item { cursor: pointer; font-weight: bold; }
</style>
{% endblock %}

{% block content %}
<div class="admin-container">
    <div class="admin-header">
        <h1><i class="fas fa-box-open"></i> 礼包管理</h1>
        <p>管理VIP等级礼包和每日礼包</p>
    </div>

    <div class="admin-panel">
        <div class="admin-panel-header">
            <h2 class="admin-panel-title">礼包列表</h2>
            <button id="addPackageBtn" class="admin-btn" style="background-color: #4CAF50; color: white;">
                <i class="fas fa-plus"></i> 添加礼包
            </button>
        </div>

        <div class="package-table-container">
            <table class="package-table" id="packageTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>图片</th>
                        <th>礼包名称</th>
                        <th>类型</th>
                        <th>VIP等级要求</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="packageTableBody">
                    <!-- 数据将由JS动态加载 -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 添加/编辑礼包模态框 -->
<div class="admin-modal" id="packageModal">
    <div class="modal-content" style="width: 95%; max-width: 1000px;">
        <div class="modal-header">
            <h2 id="modalTitle">添加礼包</h2>
            <span class="modal-close">&times;</span>
        </div>
        <div class="modal-body">
            <form id="packageForm">
                <input type="hidden" id="packageId" name="id">
                <div style="display: flex; gap: 20px;">
                    <!-- 左侧表单 -->
                    <div style="flex: 1;">
                        <div class="form-group">
                            <label for="name">礼包名称</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="description">描述</label>
                            <textarea id="description" name="description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="type">礼包类型</label>
                            <select id="type" name="type" required>
                                <option value="VIP">VIP等级礼包</option>
                                <option value="DAILY">每日礼包</option>
                            </select>
                        </div>
                        <div class="form-group" id="vipLevelGroup">
                            <label for="vip_level_required">VIP等级要求</label>
                            <input type="number" id="vip_level_required" name="vip_level_required" min="1" value="1">
                        </div>
                        <div class="form-group">
                            <label for="image_path">礼包图片URL (可选)</label>
                            <input type="text" id="image_path" name="image_path">
                        </div>
                        <div class="form-group">
                            <label>状态</label>
                            <div class="radio-group">
                                <label><input type="radio" name="is_active" value="1" checked> 启用</label>
                                <label><input type="radio" name="is_active" value="0"> 禁用</label>
                            </div>
                        </div>
                    </div>
                    <!-- 右侧物品选择 -->
                    <div style="flex: 1.5;">
                        <div class="form-group">
                            <label>包含物品 (点击下方物品进行添加/移除)</label>
                            <input type="text" id="itemSearch" class="item-search" placeholder="搜索物品...">
                            <div class="items-grid" id="itemsGrid">
                                <!-- 物品将通过JS动态加载 -->
                            </div>
                        </div>
                        <div class="form-group">
                            <label>已选物品预览</label>
                            <div class="selected-items-preview" id="selectedItemsPreview"></div>
                        </div>
                    </div>
                </div>
                <div class="form-actions">
                    <button type="submit" class="admin-btn">保存</button>
                    <button type="button" class="admin-btn cancel-btn">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="admin-modal" id="confirmDeleteModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>确认操作</h2><span class="modal-close">&times;</span>
        </div>
        <div class="modal-body">
            <p>确定要禁用这个礼包吗？</p>
            <div class="form-actions">
                <button id="confirmDeleteBtn" class="admin-btn danger-btn">确认禁用</button>
                <button class="admin-btn cancel-btn">取消</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const packageModal = document.getElementById('packageModal');
    const confirmDeleteModal = document.getElementById('confirmDeleteModal');
    const modalCloses = document.querySelectorAll('.modal-close');
    const cancelBtns = document.querySelectorAll('.cancel-btn');
    const packageForm = document.getElementById('packageForm');
    const itemsGrid = document.getElementById('itemsGrid');
    const selectedItemsPreview = document.getElementById('selectedItemsPreview');
    const typeSelect = document.getElementById('type');
    const vipLevelGroup = document.getElementById('vipLevelGroup');

    let allItems = [];
    let selectedItems = new Map(); // 使用Map存储已选物品和数量 { item_id: {name, quantity, ...} }

    // --- 数据加载 ---
    function loadPackages() {
        fetch('/api/admin/gift_packages')
            .then(res => res.json())
            .then(data => {
                if (!data.success) throw new Error(data.message);
                const tableBody = document.getElementById('packageTableBody');
                tableBody.innerHTML = '';
                data.packages.forEach(pkg => {
                    const row = `
                        <tr>
                            <td>${pkg.id}</td>
                            <td><img src="${pkg.image_path || '/static/images/items/default.png'}" class="package-image-preview"></td>
                            <td>${pkg.name}</td>
                            <td>${pkg.type}</td>
                            <td>${pkg.type === 'VIP' ? pkg.vip_level_required : 'N/A'}</td>
                            <td><span class="package-status ${pkg.is_active ? 'status-active' : 'status-inactive'}">${pkg.is_active ? '启用' : '禁用'}</span></td>
                            <td>${new Date(pkg.created_at).toLocaleString()}</td>
                            <td class="package-actions">
                                <button class="btn-edit" data-id="${pkg.id}">编辑</button>
                                <button class="btn-delete" data-id="${pkg.id}">禁用</button>
                            </td>
                        </tr>`;
                    tableBody.insertAdjacentHTML('beforeend', row);
                });
                attachActionListeners();
            })
            .catch(err => {
                console.error("加载礼包失败:", err);
                alert("加载礼包列表失败");
            });
    }

    function loadAllItems() {
        fetch('/api/all-data') // 假设这是获取所有物品的API
            .then(res => res.json())
            .then(data => {
                allItems = data.packages || []; // 只处理packages
                renderItemsGrid();
            })
            .catch(err => {
                console.error("加载物品数据失败:", err);
                alert("加载物品数据失败");
            });
    }

    function renderItemsGrid(filter = '') {
        itemsGrid.innerHTML = '';
        const lowerFilter = filter.toLowerCase();
        allItems.forEach(item => {
            if (item.packageName.toLowerCase().includes(lowerFilter)) {
                const isSelected = selectedItems.has(item.packageId);
                const card = document.createElement('div');
                card.className = `item-card ${isSelected ? 'selected' : ''}`;
                card.dataset.itemId = item.packageId;
                card.innerHTML = `
                    <img src="${item.imagePath || '/static/images/items/default.png'}" alt="${item.packageName}">
                    <div class="item-name">${item.packageName}</div>
                    ${isSelected ? `<div class="item-quantity">${selectedItems.get(item.packageId).quantity}</div>` : ''}
                `;
                card.addEventListener('click', () => toggleItemSelection(item));
                itemsGrid.appendChild(card);
            }
        });
    }

    // --- 事件处理 ---
    document.getElementById('addPackageBtn').addEventListener('click', () => openModal());
    modalCloses.forEach(el => el.addEventListener('click', () => closeModal(el.closest('.admin-modal'))));
    cancelBtns.forEach(el => el.addEventListener('click', () => closeModal(el.closest('.admin-modal'))));
    typeSelect.addEventListener('change', () => toggleVipLevelField());
    document.getElementById('itemSearch').addEventListener('input', e => renderItemsGrid(e.target.value));

    function attachActionListeners() {
        document.querySelectorAll('.btn-edit').forEach(btn => btn.addEventListener('click', e => openModal(e.target.dataset.id)));
        document.querySelectorAll('.btn-delete').forEach(btn => btn.addEventListener('click', e => confirmDelete(e.target.dataset.id)));
    }

    packageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const id = document.getElementById('packageId').value;
        const itemsArray = Array.from(selectedItems.values());
        if (itemsArray.length === 0) {
            alert("请至少选择一个物品");
            return;
        }
        const formData = {
            name: document.getElementById('name').value,
            description: document.getElementById('description').value,
            type: document.getElementById('type').value,
            vip_level_required: document.getElementById('type').value === 'VIP' ? document.getElementById('vip_level_required').value : 0,
            items: itemsArray,
            image_path: document.getElementById('image_path').value,
            is_active: document.querySelector('input[name="is_active"]:checked').value
        };

        const url = id ? `/api/admin/gift_packages/${id}` : '/api/admin/gift_packages';
        const method = id ? 'PUT' : 'POST';

        fetch(url, {
            method: method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                alert("操作成功！");
                closeModal(packageModal);
                loadPackages();
            } else {
                throw new Error(data.message);
            }
        })
        .catch(err => {
            console.error("保存礼包失败:", err);
            alert(`保存失败: ${err.message}`);
        });
    });

    let deleteId = null;
    function confirmDelete(id) {
        deleteId = id;
        openModal(confirmDeleteModal);
    }
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (!deleteId) return;
        fetch(`/api/admin/gift_packages/${deleteId}`, { method: 'DELETE' })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                alert("礼包已禁用");
                closeModal(confirmDeleteModal);
                loadPackages();
            } else {
                throw new Error(data.message);
            }
        })
        .catch(err => {
            alert(`操作失败: ${err.message}`);
        });
    });

    // --- 辅助函数 ---
    function openModal(modalOrId = packageModal) {
        if (typeof modalOrId === 'string') { // Edit mode
            fetch(`/api/admin/gift_packages`).then(res => res.json()).then(data => {
                const pkg = data.packages.find(p => p.id == modalOrId);
                if (pkg) {
                    document.getElementById('modalTitle').innerText = '编辑礼包';
                    document.getElementById('packageId').value = pkg.id;
                    document.getElementById('name').value = pkg.name;
                    document.getElementById('description').value = pkg.description;
                    document.getElementById('type').value = pkg.type;
                    document.getElementById('vip_level_required').value = pkg.vip_level_required;
                    document.getElementById('image_path').value = pkg.image_path;
                    document.querySelector(`input[name="is_active"][value="${pkg.is_active ? 1 : 0}"]`).checked = true;
                    
                    selectedItems.clear();
                    pkg.items.forEach(item => selectedItems.set(item.item_id, item));
                    
                    toggleVipLevelField();
                    updateSelectedPreview();
                    renderItemsGrid();
                    packageModal.style.display = 'block';
                }
            });
        } else if (modalOrId.id) { // A modal element
             modalOrId.style.display = 'block';
        } else { // Add mode
            document.getElementById('modalTitle').innerText = '添加礼包';
            packageForm.reset();
            selectedItems.clear();
            updateSelectedPreview();
            renderItemsGrid();
            toggleVipLevelField();
            packageModal.style.display = 'block';
        }
    }

    function closeModal(modal) {
        modal.style.display = 'none';
    }

    function toggleVipLevelField() {
        vipLevelGroup.style.display = typeSelect.value === 'VIP' ? 'block' : 'none';
    }

    function toggleItemSelection(item) {
        const itemId = item.packageId;
        if (selectedItems.has(itemId)) {
            selectedItems.delete(itemId);
        } else {
            const quantity = parseInt(prompt(`为 "${item.packageName}" 输入数量:`, "1"));
            if (quantity > 0) {
                selectedItems.set(itemId, { item_id: itemId, name: item.packageName, quantity: quantity, image_path: item.imagePath });
            }
        }
        renderItemsGrid(document.getElementById('itemSearch').value);
        updateSelectedPreview();
    }

    function updateSelectedPreview() {
        selectedItemsPreview.innerHTML = '';
        selectedItems.forEach((item, id) => {
            const tag = document.createElement('div');
            tag.className = 'selected-item-tag';
            tag.innerHTML = `<span>${item.name} (x${item.quantity})</span><span class="remove-item" data-id="${id}">&times;</span>`;
            tag.querySelector('.remove-item').addEventListener('click', (e) => {
                selectedItems.delete(e.target.dataset.id);
                updateSelectedPreview();
                renderItemsGrid(document.getElementById('itemSearch').value);
            });
            selectedItemsPreview.appendChild(tag);
        });
    }

    // --- 初始化 ---
    loadPackages();
    loadAllItems();
});
</script>
{% endblock %}
