{% extends "base.html" %}

{% block title %}赞助支持 - 7788商城{% endblock %}

{% block styles %}
<style>
/* VIP礼包样式 */
.vip-gift-package {
    margin-top: 15px;
    padding: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.gift-package-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    color: #495057;
    font-weight: 600;
    font-size: 0.9rem;
}

.gift-package-header i {
    color: #fd7e14;
}

.gift-package-content {
    min-height: 60px;
}

.loading-gift {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #6c757d;
    font-size: 0.8rem;
    padding: 20px 0;
}

.gift-package-image {
    text-align: center;
    margin-bottom: 10px;
}

.gift-package-image img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.gift-package-items {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 8px;
}

.gift-item-tag {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.gift-package-description {
    color: #6c757d;
    font-size: 0.75rem;
    line-height: 1.3;
    margin-top: 8px;
}

.no-gift-package {
    text-align: center;
    color: #adb5bd;
    font-size: 0.8rem;
    padding: 15px 0;
}

/* 优化VIP卡片布局 */
.vip-purchase-card {
    position: relative;
    transition: all 0.3s ease;
}

.vip-purchase-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .gift-package-items {
        justify-content: center;
    }

    .gift-item-tag {
        font-size: 0.65rem;
    }
}

/* VIP升级成功模态框样式 */
.upgrade-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease;
}

.upgrade-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 5% auto;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    animation: slideIn 0.3s ease;
}

.upgrade-modal-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 25px;
    text-align: center;
    color: white;
}

.upgrade-modal-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
}

.upgrade-modal-header i {
    margin-right: 10px;
    color: #ffd700;
}

.upgrade-modal-body {
    padding: 30px;
    background: white;
}

.success-message {
    text-align: center;
    margin-bottom: 25px;
}

.success-message i {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 15px;
}

.success-message p {
    font-size: 1.2rem;
    color: #2c3e50;
    margin: 0;
    font-weight: 500;
}

.gift-package-notification {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    gap: 20px;
    align-items: center;
    border: 2px solid #667eea;
}

.gift-icon {
    background: linear-gradient(135deg, #fd7e14 0%, #e17055 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
    animation: pulse 2s infinite;
}

.gift-content {
    flex: 1;
}

.gift-content h3 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 1.3rem;
}

.gift-content p {
    color: #6c757d;
    margin: 0 0 20px 0;
    line-height: 1.5;
}

.gift-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.gift-action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.gift-action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.gift-action-btn.secondary {
    background: linear-gradient(135deg, #fd7e14 0%, #e17055 100%);
    color: white;
}

.gift-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.upgrade-modal-footer {
    background: #f8f9fa;
    padding: 20px;
    text-align: center;
    border-top: 1px solid #dee2e6;
}

.close-modal-btn {
    padding: 10px 30px;
    background: #6c757d;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.close-modal-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="sponsor-container">
        <div class="sponsor-header">
            <div class="sponsor-header-content">
                <h1>支持我们的工作</h1>
                <p>您的支持是我们持续提供高质量SCUM物品代码服务的动力</p>
            </div>
        </div>

    <div class="sponsor-section">
        <h2 class="section-heading">为什么需要您的支持</h2>
        <div class="why-sponsor-grid">
            <div class="why-sponsor-card">
                <div class="sponsor-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h3>服务器维护</h3>
                <p>您的赞助将帮助我们维持高性能服务器，确保网站快速稳定运行。</p>
            </div>
            <div class="why-sponsor-card">
                <div class="sponsor-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3>内容更新</h3>
                <p>持续为您更新最新的SCUM物品代码，跟进游戏每一次的版本更新。</p>
            </div>
            <div class="why-sponsor-card">
                <div class="sponsor-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h3>功能开发</h3>
                <p>开发更多实用功能，提升用户体验，让物品代码使用更加便捷。</p>
            </div>
        </div>
    </div>

    <div class="sponsor-vip-section">
        <h2 class="section-heading">VIP会员系统</h2>
        <div class="vip-intro">
            <div class="vip-intro-content">
                <h3><i class="fas fa-crown"></i> 升级VIP，享受更多特权</h3>
                <p>我们的VIP系统分为9个等级，每个等级都有不同的特权和福利。赞助我们即可获得积分，用于升级VIP等级。<strong>1元 = 1000积分</strong></p>

                <div class="vip-benefits-list">
                    <div class="vip-benefit-item">
                        <i class="fas fa-percentage"></i>
                        <div class="vip-benefit-text">
                            <h4>商品折扣</h4>
                            <p>VIP1享受9折，VIP2享受8折，以此类推，等级越高折扣越多</p>
                        </div>
                    </div>
                    <div class="vip-benefit-item">
                        <i class="fas fa-coins"></i>
                        <div class="vip-benefit-text">
                            <h4>积分加成</h4>
                            <p>VIP1获取积分加成20%，VIP2加成40%，以此类推，等级越高加成越多</p>
                        </div>
                    </div>
                    <div class="vip-benefit-item">
                        <i class="fas fa-calendar-check"></i>
                        <div class="vip-benefit-text">
                            <h4>签到奖励</h4>
                            <p>VIP会员每日签到获得更多积分，等级越高奖励越多</p>
                        </div>
                    </div>
                </div>

                <div class="vip-purchase-section">
                    <h4>购买VIP等级</h4>
                    <div class="vip-purchase-grid">
                        {% for level in range(1, 10) %}
                            {% set points_required = level * 200000 %}
                            <div class="vip-purchase-card {% if user and user.get('vip_level', 0) >= level %}owned{% elif user and user.get('vip_level', 0) == level - 1 %}next-level{% endif %}">
                                <div class="vip-level-header">
                                    <h5>VIP {{ level }}</h5>
                                    {% if user and user.get('vip_level', 0) >= level %}
                                    <span class="level-badge owned"><i class="fas fa-check-circle"></i> 已拥有</span>
                                    {% elif user and user.get('vip_level', 0) == level - 1 %}
                                    <span class="level-badge next"><i class="fas fa-arrow-circle-up"></i> 下一级</span>
                                    {% endif %}
                                </div>
                                <div class="vip-level-price">{{ points_required }} 积分</div>
                                <div class="vip-level-benefits">
                                    <div>商品{{ (10 - level) }}折</div>
                                    <div>积分+{{ level * 20 }}%</div>
                                </div>

                                <!-- VIP升级礼包信息 -->
                                <div class="vip-gift-package" id="vip-gift-{{ level }}">
                                    <div class="gift-package-header">
                                        <i class="fas fa-gift"></i>
                                        <span>升级礼包</span>
                                    </div>
                                    <div class="gift-package-content">
                                        <!-- 礼包内容将通过JavaScript动态加载 -->
                                        <div class="loading-gift">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            <span>加载中...</span>
                                        </div>
                                    </div>
                                </div>
                                {% if user %}
                                    {% if user.get('vip_level', 0) >= level %}
                                    <button class="vip-buy-btn owned" disabled>已拥有</button>
                                    {% elif user.get('vip_level', 0) == level - 1 %}
                                    <button class="vip-buy-btn upgrade" data-level="{{ level }}" data-points="{{ points_required }}">立即升级</button>
                                    {% else %}
                                    <button class="vip-buy-btn locked" disabled>需先购买前置等级</button>
                                    {% endif %}
                                {% else %}
                                <button class="vip-buy-btn locked" disabled>请先登录</button>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="sponsor-qrcode-section">
        <h2 class="section-heading">扫码赞助</h2>
        <div class="sponsor-qrcode">
            <div class="qrcode-images">
                <div class="qrcode">
                    <img src="{{ url_for('static', filename='img/wechat-pay.jpg') }}" alt="微信支付">
                    <p>微信支付</p>
                </div>
                <div class="qrcode">
                    <img src="{{ url_for('static', filename='img/alipay.jpg') }}" alt="支付宝">
                    <p>支付宝</p>
                </div>
            </div>
            <p class="sponsor-note">赞助后请联系客服QQ: 2397146940 获取赞助特权</p>
        </div>
    </div>
    </div>
</div>

<!-- VIP升级成功模态框 -->
<div id="upgradeSuccessModal" class="upgrade-modal">
    <div class="upgrade-modal-content">
        <div class="upgrade-modal-header">
            <h2><i class="fas fa-crown"></i> 升级成功！</h2>
        </div>
        <div class="upgrade-modal-body">
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <p id="upgradeMessage">恭喜您成功升级到VIP等级！</p>
            </div>
            <div class="gift-package-notification" id="giftNotification">
                <div class="gift-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <div class="gift-content">
                    <h3>专属升级礼包已解锁！</h3>
                    <p>您可以前往VIP页面或每日礼包页面领取您的专属礼包</p>
                    <div class="gift-actions">
                        <button id="goToVipBtn" class="gift-action-btn primary">
                            <i class="fas fa-crown"></i> 前往VIP页面
                        </button>
                        <button id="goToGiftsBtn" class="gift-action-btn secondary">
                            <i class="fas fa-gift"></i> 前往礼包页面
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="upgrade-modal-footer">
            <button id="closeUpgradeModal" class="close-modal-btn">
                <i class="fas fa-times"></i> 关闭
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 加载VIP礼包信息
    loadVipGiftPackages();

    // 获取所有升级按钮
    const upgradeButtons = document.querySelectorAll('.vip-buy-btn.upgrade');

    // 为升级按钮添加点击事件
    upgradeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const level = this.getAttribute('data-level');
            const points = this.getAttribute('data-points');

            if (confirm(`您确定要升级到 VIP${level} 吗？\n需要消费 ${points} 积分`)) {
                // 发送升级请求
                fetch('/upgrade_vip', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        level: parseInt(level)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示成功消息并提示礼包
                        showUpgradeSuccessModal(level, data.message);
                    } else {
                        // 显示错误消息
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('升级VIP失败:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        });
    });

    // 加载VIP礼包信息
    function loadVipGiftPackages() {
        fetch('/api/gift_packages')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const vipPackages = data.packages.filter(p => p.type === 'VIP');
                    displayVipGiftPackages(vipPackages);
                } else {
                    console.error('加载VIP礼包失败:', data.message);
                    showNoGiftPackages();
                }
            })
            .catch(error => {
                console.error('网络错误:', error);
                showNoGiftPackages();
            });
    }

    function displayVipGiftPackages(vipPackages) {
        // 为每个VIP等级显示对应的礼包
        for (let level = 1; level <= 9; level++) {
            const giftContainer = document.getElementById(`vip-gift-${level}`);
            if (!giftContainer) continue;

            const contentDiv = giftContainer.querySelector('.gift-package-content');
            const matchingPackage = vipPackages.find(p => p.vip_level_required === level);

            if (matchingPackage) {
                // 显示礼包信息
                const itemsHtml = matchingPackage.items.slice(0, 4).map(item =>
                    `<span class="gift-item-tag">${item.name} x${item.quantity}</span>`
                ).join('');

                const moreItemsText = matchingPackage.items.length > 4 ?
                    `<span class="gift-item-tag">+${matchingPackage.items.length - 4}更多</span>` : '';

                contentDiv.innerHTML = `
                    <div class="gift-package-image">
                        <img src="${matchingPackage.image_path || '/static/images/gift-default.png'}" alt="${matchingPackage.name}">
                    </div>
                    <div class="gift-package-items">
                        ${itemsHtml}
                        ${moreItemsText}
                    </div>
                    <div class="gift-package-description">
                        ${matchingPackage.description || '升级到此等级即可获得丰厚奖励！'}
                    </div>
                `;
            } else {
                // 没有对应礼包
                contentDiv.innerHTML = `
                    <div class="no-gift-package">
                        <i class="fas fa-info-circle"></i>
                        <span>暂无升级礼包</span>
                    </div>
                `;
            }
        }
    }

    function showNoGiftPackages() {
        // 如果加载失败，显示默认信息
        for (let level = 1; level <= 9; level++) {
            const giftContainer = document.getElementById(`vip-gift-${level}`);
            if (!giftContainer) continue;

            const contentDiv = giftContainer.querySelector('.gift-package-content');
            contentDiv.innerHTML = `
                <div class="no-gift-package">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>加载失败</span>
                </div>
            `;
        }
    }

    // VIP升级成功模态框处理
    function showUpgradeSuccessModal(level, message) {
        const modal = document.getElementById('upgradeSuccessModal');
        const messageElement = document.getElementById('upgradeMessage');

        messageElement.textContent = message;
        modal.style.display = 'block';

        // 检查是否有对应的VIP礼包
        checkVipGiftPackage(level);
    }

    function checkVipGiftPackage(level) {
        fetch('/api/gift_packages')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const vipPackage = data.packages.find(p => p.type === 'VIP' && p.vip_level_required === parseInt(level));
                    const giftNotification = document.getElementById('giftNotification');

                    if (vipPackage && vipPackage.can_claim) {
                        // 有可领取的礼包，显示礼包通知
                        giftNotification.style.display = 'block';
                    } else {
                        // 没有可领取的礼包，隐藏礼包通知
                        giftNotification.style.display = 'none';
                    }
                }
            })
            .catch(error => {
                console.error('检查礼包失败:', error);
                // 出错时隐藏礼包通知
                document.getElementById('giftNotification').style.display = 'none';
            });
    }

    // 模态框事件处理
    document.getElementById('closeUpgradeModal').addEventListener('click', function() {
        document.getElementById('upgradeSuccessModal').style.display = 'none';
        // 关闭模态框后刷新页面
        window.location.reload();
    });

    document.getElementById('goToVipBtn').addEventListener('click', function() {
        window.location.href = '/vip';
    });

    document.getElementById('goToGiftsBtn').addEventListener('click', function() {
        window.location.href = '/daily_gifts';
    });

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('upgradeSuccessModal');
        if (event.target === modal) {
            modal.style.display = 'none';
            window.location.reload();
        }
    });
});
</script>
{% endblock %}