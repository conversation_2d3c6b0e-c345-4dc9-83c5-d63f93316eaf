{% extends "base.html" %}

{% block title %}赞助支持 - 7788商城{% endblock %}
{% block content %}
<div class="container">
    <div class="sponsor-container">
        <div class="sponsor-header">
            <div class="sponsor-header-content">
                <h1>支持我们的工作</h1>
                <p>您的支持是我们持续提供高质量SCUM物品代码服务的动力</p>
            </div>
        </div>

    <div class="sponsor-section">
        <h2 class="section-heading">为什么需要您的支持</h2>
        <div class="why-sponsor-grid">
            <div class="why-sponsor-card">
                <div class="sponsor-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h3>服务器维护</h3>
                <p>您的赞助将帮助我们维持高性能服务器，确保网站快速稳定运行。</p>
            </div>
            <div class="why-sponsor-card">
                <div class="sponsor-icon">
                    <i class="fas fa-code"></i>
                </div>
                <h3>内容更新</h3>
                <p>持续为您更新最新的SCUM物品代码，跟进游戏每一次的版本更新。</p>
            </div>
            <div class="why-sponsor-card">
                <div class="sponsor-icon">
                    <i class="fas fa-tools"></i>
                </div>
                <h3>功能开发</h3>
                <p>开发更多实用功能，提升用户体验，让物品代码使用更加便捷。</p>
            </div>
        </div>
    </div>

    <div class="sponsor-vip-section">
        <h2 class="section-heading">VIP会员系统</h2>
        <div class="vip-intro">
            <div class="vip-intro-content">
                <h3><i class="fas fa-crown"></i> 升级VIP，享受更多特权</h3>
                <p>我们的VIP系统分为9个等级，每个等级都有不同的特权和福利。赞助我们即可获得积分，用于升级VIP等级。<strong>1元 = 1000积分</strong></p>

                <div class="vip-benefits-list">
                    <div class="vip-benefit-item">
                        <i class="fas fa-percentage"></i>
                        <div class="vip-benefit-text">
                            <h4>商品折扣</h4>
                            <p>VIP1享受9折，VIP2享受8折，以此类推，等级越高折扣越多</p>
                        </div>
                    </div>
                    <div class="vip-benefit-item">
                        <i class="fas fa-coins"></i>
                        <div class="vip-benefit-text">
                            <h4>积分加成</h4>
                            <p>VIP1获取积分加成20%，VIP2加成40%，以此类推，等级越高加成越多</p>
                        </div>
                    </div>
                    <div class="vip-benefit-item">
                        <i class="fas fa-calendar-check"></i>
                        <div class="vip-benefit-text">
                            <h4>签到奖励</h4>
                            <p>VIP会员每日签到获得更多积分，等级越高奖励越多</p>
                        </div>
                    </div>
                </div>

                <div class="vip-purchase-section">
                    <h4>购买VIP等级</h4>
                    <div class="vip-purchase-grid">
                        {% for level in range(1, 10) %}
                            {% set points_required = level * 200000 %}
                            <div class="vip-purchase-card {% if user and user.get('vip_level', 0) >= level %}owned{% elif user and user.get('vip_level', 0) == level - 1 %}next-level{% endif %}">
                                <div class="vip-level-header">
                                    <h5>VIP {{ level }}</h5>
                                    {% if user and user.get('vip_level', 0) >= level %}
                                    <span class="level-badge owned"><i class="fas fa-check-circle"></i> 已拥有</span>
                                    {% elif user and user.get('vip_level', 0) == level - 1 %}
                                    <span class="level-badge next"><i class="fas fa-arrow-circle-up"></i> 下一级</span>
                                    {% endif %}
                                </div>
                                <div class="vip-level-price">{{ points_required }} 积分</div>
                                <div class="vip-level-benefits">
                                    <div>商品{{ (10 - level) }}折</div>
                                    <div>积分+{{ level * 20 }}%</div>
                                </div>

                                <!-- VIP升级礼包信息 -->
                                <div class="vip-gift-package" id="vip-gift-{{ level }}">
                                    <div class="gift-package-header">
                                        <i class="fas fa-gift"></i>
                                        <span>升级礼包</span>
                                    </div>
                                    <div class="gift-package-content">
                                        <!-- 礼包内容将通过JavaScript动态加载 -->
                                        <div class="loading-gift">
                                            <i class="fas fa-spinner fa-spin"></i>
                                            <span>加载中...</span>
                                        </div>
                                    </div>
                                </div>
                                {% if user %}
                                    {% if user.get('vip_level', 0) >= level %}
                                    <button class="vip-buy-btn owned" disabled>已拥有</button>
                                    {% elif user.get('vip_level', 0) == level - 1 %}
                                    <button class="vip-buy-btn upgrade" data-level="{{ level }}" data-points="{{ points_required }}">立即升级</button>
                                    {% else %}
                                    <button class="vip-buy-btn locked" disabled>需先购买前置等级</button>
                                    {% endif %}
                                {% else %}
                                <button class="vip-buy-btn locked" disabled>请先登录</button>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="sponsor-qrcode-section">
        <h2 class="section-heading">扫码赞助</h2>
        <div class="sponsor-qrcode">
            <div class="qrcode-images">
                <div class="qrcode">
                    <img src="{{ url_for('static', filename='img/wechat-pay.jpg') }}" alt="微信支付">
                    <p>微信支付</p>
                </div>
                <div class="qrcode">
                    <img src="{{ url_for('static', filename='img/alipay.jpg') }}" alt="支付宝">
                    <p>支付宝</p>
                </div>
            </div>
            <p class="sponsor-note">赞助后请联系客服QQ: 2397146940 获取赞助特权</p>
        </div>
    </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有升级按钮
    const upgradeButtons = document.querySelectorAll('.vip-buy-btn.upgrade');

    // 为升级按钮添加点击事件
    upgradeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const level = this.getAttribute('data-level');
            const points = this.getAttribute('data-points');

            if (confirm(`您确定要升级到 VIP${level} 吗？\n需要消费 ${points} 积分`)) {
                // 发送升级请求
                fetch('/upgrade_vip', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        level: parseInt(level)
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示成功消息
                        alert(data.message);
                        // 刷新页面
                        window.location.reload();
                    } else {
                        // 显示错误消息
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('升级VIP失败:', error);
                    alert('网络错误，请稍后重试');
                });
            }
        });
    });
});
</script>
{% endblock %}