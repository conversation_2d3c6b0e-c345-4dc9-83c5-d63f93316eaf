-- ----------------------------
-- Table structure for gift_packages
-- ----------------------------
DROP TABLE IF EXISTS `gift_packages`;
CREATE TABLE `gift_packages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '礼包名称',
  `description` text COLLATE utf8mb4_unicode_ci,
  `type` enum('VIP','DAILY') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '礼包类型: VIP等级礼包, DAILY每日礼包',
  `vip_level_required` int(11) DEFAULT '0' COMMENT '领取所需的VIP等级 (仅对VIP类型有效)',
  `items` json NOT NULL COMMENT '物品列表，格式为 [{"item_id": "code", "name": "名称", "quantity": 1}]',
  `image_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '礼包展示图片路径',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 (1:是, 0:否)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for gift_claim_logs
-- ----------------------------
DROP TABLE IF EXISTS `gift_claim_logs`;
CREATE TABLE `gift_claim_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `steamid` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `package_id` int(11) NOT NULL,
  `claimed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_steamid_package` (`steamid`,`package_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
