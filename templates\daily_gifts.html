{% extends "base.html" %}

{% block title %}每日礼包 - 7788商城{% endblock %}

{% block content %}
<div class="daily-gifts-container">
    <!-- 页面标题 -->
    <div class="daily-gifts-header">
        <div class="header-content">
            <h1><i class="fas fa-gift"></i> 每日礼包中心</h1>
            <p>每日登录即可领取丰厚奖励，VIP用户享受更多特权礼包</p>
        </div>
    </div>

    <!-- 说明区域 -->
    <div class="daily-gifts-info">
        <div class="info-card">
            <div class="info-icon">
                <i class="fas fa-info-circle"></i>
            </div>
            <div class="info-content">
                <h3>礼包说明</h3>
                <ul>
                    <li><strong>普通礼包：</strong>所有用户每日可领取一次</li>
                    <li><strong>VIP礼包：</strong>根据VIP等级划分，等级越高奖励越丰厚</li>
                    <li><strong>领取时间：</strong>每日00:00重置，请及时领取</li>
                    <li><strong>物品发放：</strong>领取后物品将自动添加到您的仓库</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 普通每日礼包 -->
    <div class="daily-gifts-section">
        <h2 class="section-title">
            <i class="fas fa-box"></i> 普通每日礼包
        </h2>
        <div id="normal-packages-container" class="packages-grid">
            <!-- 普通礼包将通过JS动态加载 -->
        </div>
    </div>

    <!-- VIP每日礼包 -->
    <div class="daily-gifts-section">
        <h2 class="section-title">
            <i class="fas fa-crown"></i> VIP每日礼包
        </h2>
        <div class="vip-packages-info">
            <p>VIP用户专享礼包，等级越高奖励越丰厚！还不是VIP？<a href="/sponsor" class="upgrade-link">立即升级</a></p>
        </div>
        <div id="vip-packages-container" class="packages-grid">
            <!-- VIP礼包将通过JS动态加载 -->
        </div>
    </div>
</div>

<!-- 礼包详情模态框 -->
<div id="packageModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="modalTitle">礼包详情</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <div class="package-detail">
                <div class="package-image">
                    <img id="modalImage" src="" alt="礼包图片">
                </div>
                <div class="package-info">
                    <h4 id="modalPackageName"></h4>
                    <p id="modalPackageDesc"></p>
                    <div class="package-items" id="modalPackageItems">
                        <!-- 物品列表 -->
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button id="modalClaimBtn" class="claim-btn">
                <i class="fas fa-gift"></i> 领取礼包
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.daily-gifts-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.daily-gifts-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header-content h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.header-content p {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin: 0;
}

.daily-gifts-info {
    margin-bottom: 40px;
}

.info-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: flex-start;
    gap: 20px;
}

.info-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.info-content h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.info-content ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.info-content li {
    color: #5a6c7d;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
}

.info-content li::before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.daily-gifts-section {
    margin-bottom: 50px;
}

.section-title {
    color: #2c3e50;
    font-size: 1.8rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 3px solid #667eea;
    display: inline-block;
}

.vip-packages-info {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.vip-packages-info p {
    margin: 0;
    color: #2d3436;
    font-weight: 500;
}

.upgrade-link {
    color: #e17055;
    text-decoration: none;
    font-weight: 600;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.upgrade-link:hover {
    border-bottom-color: #e17055;
}

.packages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 25px;
}

.package-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.package-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.package-card.claimed {
    opacity: 0.7;
    background: rgba(236, 240, 241, 0.95);
}

.package-card.claimed::before {
    background: #95a5a6;
}

.package-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.package-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.3rem;
}

.vip-level-badge {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.package-image {
    text-align: center;
    margin-bottom: 15px;
}

.package-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.package-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 15px;
    line-height: 1.5;
}

.package-items-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
}

.item-preview {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.8rem;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 5px;
}

.item-preview i {
    color: #667eea;
}

.claim-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.claim-btn:not(:disabled) {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
}

.claim-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
}

.claim-btn:disabled {
    background: #bdc3c7;
    color: #7f8c8d;
    cursor: not-allowed;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.4rem;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.close:hover {
    opacity: 0.7;
}

.modal-body {
    padding: 25px;
}

.package-detail {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.modal .package-image img {
    width: 120px;
    height: 120px;
}

.modal .package-info {
    flex: 1;
}

.modal .package-info h4 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.modal .package-info p {
    color: #7f8c8d;
    margin-bottom: 15px;
    line-height: 1.5;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #ecf0f1;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .daily-gifts-container {
        padding: 15px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .packages-grid {
        grid-template-columns: 1fr;
    }
    
    .package-detail {
        flex-direction: column;
        text-align: center;
    }
    
    .info-card {
        flex-direction: column;
        text-align: center;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const normalPackagesContainer = document.getElementById('normal-packages-container');
    const vipPackagesContainer = document.getElementById('vip-packages-container');
    const modal = document.getElementById('packageModal');
    const closeBtn = document.querySelector('.close');
    let currentPackageId = null;

    // 加载礼包数据
    loadGiftPackages();

    // 模态框事件
    closeBtn.addEventListener('click', closeModal);
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeModal();
        }
    });

    function loadGiftPackages() {
        fetch('/api/gift_packages')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderPackages(data.packages);
                } else {
                    showError('加载礼包失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('网络错误，请稍后重试');
            });
    }

    function renderPackages(packages) {
        const normalPackages = packages.filter(p => p.type === 'DAILY' && p.vip_level_required === 0);
        const vipPackages = packages.filter(p => p.type === 'DAILY' && p.vip_level_required > 0);

        // 渲染普通礼包
        if (normalPackages.length === 0) {
            normalPackagesContainer.innerHTML = '<div class="no-packages">暂无普通每日礼包</div>';
        } else {
            normalPackagesContainer.innerHTML = normalPackages.map(pkg => createPackageCard(pkg)).join('');
        }

        // 渲染VIP礼包
        if (vipPackages.length === 0) {
            vipPackagesContainer.innerHTML = '<div class="no-packages">暂无VIP每日礼包</div>';
        } else {
            // 按VIP等级排序
            vipPackages.sort((a, b) => a.vip_level_required - b.vip_level_required);
            vipPackagesContainer.innerHTML = vipPackages.map(pkg => createPackageCard(pkg, true)).join('');
        }

        // 绑定事件
        bindPackageEvents();
    }

    function createPackageCard(pkg, isVip = false) {
        const itemsPreview = pkg.items.slice(0, 3).map(item =>
            `<div class="item-preview">
                <i class="fas fa-cube"></i>
                ${item.name} x${item.quantity}
            </div>`
        ).join('');

        const moreItemsText = pkg.items.length > 3 ? `<div class="item-preview">+${pkg.items.length - 3}更多...</div>` : '';

        return `
            <div class="package-card ${pkg.can_claim ? '' : 'claimed'}" data-package-id="${pkg.id}">
                <div class="package-header">
                    <h3>${pkg.name}</h3>
                    ${isVip ? `<span class="vip-level-badge">VIP ${pkg.vip_level_required}+</span>` : ''}
                </div>
                <div class="package-image">
                    <img src="${pkg.image_path || '/static/images/gift-default.png'}" alt="${pkg.name}">
                </div>
                <div class="package-description">
                    ${pkg.description || '每日登录即可领取丰厚奖励'}
                </div>
                <div class="package-items-preview">
                    ${itemsPreview}
                    ${moreItemsText}
                </div>
                <button class="claim-btn ${pkg.can_claim ? 'claimable' : ''}"
                        data-package-id="${pkg.id}"
                        ${pkg.can_claim ? '' : 'disabled'}>
                    ${pkg.can_claim ? '<i class="fas fa-gift"></i> 立即领取' : `<i class="fas fa-lock"></i> ${pkg.reason}`}
                </button>
            </div>
        `;
    }

    function bindPackageEvents() {
        // 礼包卡片点击事件（显示详情）
        document.querySelectorAll('.package-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (!e.target.classList.contains('claim-btn')) {
                    const packageId = this.getAttribute('data-package-id');
                    showPackageDetail(packageId);
                }
            });
        });

        // 领取按钮点击事件
        document.querySelectorAll('.claim-btn.claimable').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.stopPropagation();
                const packageId = this.getAttribute('data-package-id');
                claimPackage(packageId, this);
            });
        });
    }

    function showPackageDetail(packageId) {
        fetch('/api/gift_packages')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const pkg = data.packages.find(p => p.id == packageId);
                    if (pkg) {
                        displayPackageModal(pkg);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
    }

    function displayPackageModal(pkg) {
        document.getElementById('modalTitle').textContent = '礼包详情';
        document.getElementById('modalPackageName').textContent = pkg.name;
        document.getElementById('modalPackageDesc').textContent = pkg.description || '每日登录即可领取丰厚奖励';
        document.getElementById('modalImage').src = pkg.image_path || '/static/images/gift-default.png';

        const itemsHtml = pkg.items.map(item =>
            `<div class="item-detail">
                <i class="fas fa-cube"></i>
                <span>${item.name} x${item.quantity}</span>
            </div>`
        ).join('');
        document.getElementById('modalPackageItems').innerHTML = itemsHtml;

        const claimBtn = document.getElementById('modalClaimBtn');
        currentPackageId = pkg.id;

        if (pkg.can_claim) {
            claimBtn.disabled = false;
            claimBtn.innerHTML = '<i class="fas fa-gift"></i> 领取礼包';
            claimBtn.onclick = () => claimPackage(pkg.id, claimBtn);
        } else {
            claimBtn.disabled = true;
            claimBtn.innerHTML = `<i class="fas fa-lock"></i> ${pkg.reason}`;
        }

        modal.style.display = 'block';
    }

    function claimPackage(packageId, button) {
        if (!packageId) return;

        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 领取中...';

        fetch('/api/gift_packages/claim', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                package_id: parseInt(packageId)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message);
                closeModal();
                // 重新加载礼包数据
                setTimeout(() => {
                    loadGiftPackages();
                }, 1000);
            } else {
                showError(data.message);
                button.disabled = false;
                button.innerHTML = '<i class="fas fa-gift"></i> 领取礼包';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('网络错误，请稍后重试');
            button.disabled = false;
            button.innerHTML = '<i class="fas fa-gift"></i> 领取礼包';
        });
    }

    function closeModal() {
        modal.style.display = 'none';
        currentPackageId = null;
    }

    function showSuccess(message) {
        // 创建成功提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-success';
        alert.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 3000);
    }

    function showError(message) {
        // 创建错误提示
        const alert = document.createElement('div');
        alert.className = 'alert alert-error';
        alert.innerHTML = `
            <i class="fas fa-exclamation-circle"></i>
            <span>${message}</span>
        `;
        document.body.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
});
</script>

<style>
/* 添加模态框中的物品详情样式 */
.item-detail {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
    color: #2c3e50;
}

.item-detail:last-child {
    border-bottom: none;
}

.item-detail i {
    color: #667eea;
    width: 16px;
}

/* 无礼包提示样式 */
.no-packages {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    border: 2px dashed #bdc3c7;
}

/* 提示框样式 */
.alert {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 300px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    animation: slideIn 0.3s ease;
}

.alert-success {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
}

.alert-error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>
{% endblock %}
