#!/usr/bin/env python3
"""
简单的通行证API测试
"""

import requests
import json

# 测试数据
TEST_DATA = {
    'steam_id': '76561199251655648',  # 使用一个真实的Steam ID
    'quest_name': '击杀敌人'
}

def test_quest_complete():
    """测试任务完成API"""
    url = "http://211.101.247.156:7788/api/quest/complete"
    
    try:
        print("测试任务完成API...")
        print(f"URL: {url}")
        print(f"数据: {json.dumps(TEST_DATA, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=TEST_DATA, headers={'Content-Type': 'application/json'})
        
        print(f"\n状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("\n✅ 任务完成成功!")
                data = result.get('data', {})
                print(f"获得经验: {data.get('exp_gained', 0)}")
                print(f"总经验: {data.get('total_exp', 0)}")
                print(f"等级变化: {data.get('old_level', 0)} -> {data.get('new_level', 0)}")
                if data.get('level_up'):
                    print("🎉 升级了!")
                    print(f"积分奖励: {data.get('points_reward', 0)}")
                print(f"今日进度: {data.get('daily_quest_count', 0)}/{data.get('daily_quest_limit', 20)}")
            else:
                print(f"❌ 失败: {result.get('message', '未知错误')}")
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    test_quest_complete()
